@import "/styles/component-theme.wxss";

.text-toolbar {
  width: 100%;
  background-color: var(--card-background);
  border-top: 2rpx solid var(--divider-color);
  box-shadow: 0 -4rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

.text-toolbar.show {
  display: block;
}

.text-toolbar.hidden {
  display: none;
}

.toolbar-main {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16rpx 0;
  width: 100%;
  flex-wrap: wrap;
}

.toolbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx;
  min-width: 80rpx;
}

.toolbar-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-bottom: 8rpx;
  color: var(--primary-color);
}

.toolbar-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

.icon-font {
  font-weight: bold;
  font-family: Arial, sans-serif;
}

/* 字体设置面板背景遮罩 */
.font-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 899;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
}

.font-panel-mask.show {
  visibility: visible;
  opacity: 1;
}

/* 面板通用样式 */
.font-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--card-background);
  border-top-left-radius: var(--border-radius-lg);
  border-top-right-radius: var(--border-radius-lg);
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  padding: 24rpx;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  z-index: 900;
  box-sizing: border-box;
  max-height: 60vh;
  overflow-y: auto;
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.font-panel.show {
  transform: translateY(0);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.panel-title {
  font-size: var(--font-size-md);
  font-weight: bold;
  color: var(--text-primary);
}

.panel-close {
  font-size: 40rpx;
  color: var(--text-secondary);
  line-height: 1;
  padding: 10rpx;
}

/* 字体设置面板样式 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.setting-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.setting-control {
  display: flex;
  align-items: center;
}

.control-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light);
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  font-weight: bold;
  border-radius: var(--border-radius-sm);
}

.control-value {
  width: 80rpx;
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin: 0 10rpx;
}

/* 搜索面板样式 */
.search-input-container {
  display: flex;
  align-items: center;
}

.search-input {
  flex: 1;
  background-color: var(--primary-light);
  border-radius: var(--border-radius-md);
  padding: 16rpx 20rpx;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-right: 16rpx;
}

.search-btn {
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius-md);
  padding: 16rpx 30rpx;
  font-size: var(--font-size-sm);
} 